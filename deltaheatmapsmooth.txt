//@version=6
indicator("Delta Heatmap Smooth", shorttitle="DeltaHeatmap", overlay=true)

// Input parameters
smoothing_period = input.int(20, "Lookback Smoothing (bars)", minval=1, maxval=500)
gradient_scale = input.float(1.0, "Gradient Scale (× Avg|Δ|)", minval=0.1, maxval=10.0, step=0.1)
strong_buy_color = input.color(color.new(color.blue, 0), "Strong Buy Color")
strong_sell_color = input.color(color.new(color.red, 0), "Strong Sell Color")
neutral_color = input.color(color.new(color.gray, 0), "Neutral Color")
extreme_threshold = input.float(0.85, "Extreme Color Threshold", minval=0.8, maxval=1.0, step=0.01)
minimum_threshold = input.float(0.1, "Minimum Color Threshold", minval=0.0, maxval=0.3, step=0.01)

// Determine the appropriate lower timeframe based on current chart timeframe
var lowerTimeframe = switch
    timeframe.isseconds     => "1S"
    timeframe.isintraday    => "1"
    timeframe.isdaily       => "5"
    => "60"

// Get volume delta data using TradingView's built-in function
[openVolume, maxVolume, minVolume, lastVolume] = request.volume_delta(lowerTimeframe)

// Use actual volume delta data
volume_delta = not na(lastVolume) ? lastVolume : 0.0

// Calculate absolute delta
abs_delta = math.abs(volume_delta)

// Apply smoothing using weighted moving average for delta and simple moving average for absolute delta
smoothed_delta = ta.wma(volume_delta, smoothing_period)
avg_abs_delta = ta.sma(abs_delta, smoothing_period)

// Calculate normalized delta
var float normalized_delta = 0.0
if avg_abs_delta > 0 and gradient_scale > 0
    normalized_delta := smoothed_delta / (avg_abs_delta * gradient_scale)
    // Clamp to [-1, 1]
    normalized_delta := math.max(-1.0, math.min(1.0, normalized_delta))

// Calculate bar color based on normalized delta
get_bar_color() =>
    if normalized_delta >= 0
        color.from_gradient(normalized_delta, 0, 1, neutral_color, strong_buy_color)
    else
        color.from_gradient(-normalized_delta, 0, 1, neutral_color, strong_sell_color)

// Apply color only after we have enough data
bar_color = bar_index >= smoothing_period - 1 ? get_bar_color() : na

// Plot colored bars
plotcandle(open, high, low, close, color=bar_color, wickcolor=bar_color, bordercolor=bar_color, title="Delta Heatmap")

// Plot extreme buy circles (blue circles at highs)
extreme_buy_condition = normalized_delta >= extreme_threshold and bar_index >= smoothing_period - 1
plotshape(extreme_buy_condition ? high : na, style=shape.circle, location=location.absolute, color=strong_buy_color,
          size=size.normal, title="Extreme Buy Circles")

// Plot extreme sell circles (red circles at lows)
extreme_sell_condition = normalized_delta <= -extreme_threshold and bar_index >= smoothing_period - 1
plotshape(extreme_sell_condition ? low : na, style=shape.circle, location=location.absolute, color=strong_sell_color,
          size=size.normal, title="Extreme Sell Circles")

// Plot minimum threshold positive dots (light blue dots at highs)
min_threshold_pos_condition = normalized_delta >= 0 and math.abs(normalized_delta) <= minimum_threshold and bar_index >= smoothing_period - 1
plotshape(min_threshold_pos_condition ? high : na, style=shape.circle, location=location.absolute, color=color.new(color.blue, 60),
          size=size.small, title="Minimum Threshold Positive Dots")

// Plot minimum threshold negative dots (light red dots at lows)
min_threshold_neg_condition = normalized_delta < 0 and math.abs(normalized_delta) <= minimum_threshold and bar_index >= smoothing_period - 1
plotshape(min_threshold_neg_condition ? low : na, style=shape.circle, location=location.absolute, color=color.new(color.red, 60),
          size=size.small, title="Minimum Threshold Negative Dots")

// Optional: Plot smoothed delta as a line (hidden by default)
plot(smoothed_delta, title="Smoothed Delta", display=display.none)

// Optional: Plot average absolute delta (hidden by default)
plot(avg_abs_delta, title="Average Absolute Delta", display=display.none)

// Error handling for missing volume data
var cumVol = 0.
cumVol += nz(volume)
if barstate.islast and cumVol == 0
    runtime.error("The data vendor doesn't provide volume data for this symbol.")

// Table to show current values (optional)
if barstate.islast
    var table info_table = table.new(position.top_right, 2, 4, bgcolor=color.white, border_width=1)
    table.cell(info_table, 0, 0, "Smoothed Delta:", text_color=color.black)
    table.cell(info_table, 1, 0, str.tostring(smoothed_delta, "#.##"), text_color=color.black)
    table.cell(info_table, 0, 1, "Avg |Delta|:", text_color=color.black)
    table.cell(info_table, 1, 1, str.tostring(avg_abs_delta, "#.##"), text_color=color.black)
    table.cell(info_table, 0, 2, "Normalized:", text_color=color.black)
    table.cell(info_table, 1, 2, str.tostring(normalized_delta, "#.###"), text_color=color.black)
    table.cell(info_table, 0, 3, "Bar Color:", text_color=color.black)
    table.cell(info_table, 1, 3, "", bgcolor=bar_color)